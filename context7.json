{"$schema": "https://context7.com/schema/context7.json", "projectTitle": "HTTP<PERSON><PERSON>", "description": "A Cloudflare Workers implementation of HTTPBin for testing HTTP requests and responses", "folders": ["src", "test"], "excludeFolders": ["node_modules", ".git"], "excludeFiles": ["*.log", "*.tmp"], "rules": ["Use Cloudflare Workers runtime APIs", "Follow modern JavaScript/ES6+ patterns", "Implement HTTP testing endpoints similar to httpbin.org", "Use Vitest for testing", "Follow RESTful API conventions"]}