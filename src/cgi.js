const HttpMethod = {
  GET: Symbol("GET"),
  POST: Symbol("POST"),
  PUT: Symbol("PUT"),
  DELETE: Symbol("DELETE"),
  PATCH: Symbol("PATCH"),
}

const ContentType = {
  JSON: Symbol("application/json"),
  FORM: Symbol("application/x-www-form-urlencoded"),
  TEXT: Symbol("text/plain"),
}

/**
 * @param {number} code HTTP status code
 * @returns {Response}
 */
async function genErrorResponse(code) {
  /**
   * @type {string}
   */
  let msg;
  switch (code) {
    case 400:
      msg = "Bad Request";
      break;
    case 401:
      msg = "Unauthorized";
      break;
    case 403:
      msg = "Forbidden";
      break;
    case 404:
    default:
      msg = "Not Found";
      break;
  }
  return new Response(msg, {
    status: code,
    statusText: msg,
    headers: {
      "content-type": "text/plain",
    },
  });
}

/**
 * @param {Request} request 
 * @returns {Response}
 */
async function Parse(request, env, ctx) {
  const url = URL.parse(request.url);

  /**
   * @type {HttpMethod}
   */
  let method;

  if (url.pathname === "/") {
    return genErrorResponse(404);
  } else if (url.pathname.startsWith("/get")) {
    if (request.method !== "GET") {
      return genErrorResponse(400);
    }
    method = HttpMethod.GET;
  } else if (url.pathname.startsWith("/post")) {
    if (request.method !== "POST") {
      return genErrorResponse(400);
    }
    method = HttpMethod.POST;
  } else if (url.pathname.startsWith("/put")) {
    if (request.method !== "PUT") {
      return genErrorResponse(400);
    }
    method = HttpMethod.PUT;
  } else if (url.pathname.startsWith("/delete")) {
    if (request.method !== "DELETE") {
      return genErrorResponse(400);
    }
    method = HttpMethod.DELETE;
  } else if (url.pathname.startsWith("/patch")) {
    if (request.method !== "PATCH") {
      return genErrorResponse(400);
    }
    method = HttpMethod.PATCH;
  } else {
    return genErrorResponse(404);
  }

  /**
   * @type {ContentType}
   */
  let contentType;
  const contentTypeHeader = request.headers.get("content-type");
  if (contentTypeHeader === null) {
    contentType = ContentType.JSON;
  } else if (contentTypeHeader === "application/json") {
    contentType = ContentType.JSON;
  } else if (contentTypeHeader === "application/x-www-form-urlencoded") {
    contentType = ContentType.FORM;
  } else if (contentTypeHeader === "text/plain") {
    contentType = ContentType.TEXT;
  } else {
    return genErrorResponse(400);
  }

  let retJson = {
    args: {},
    headers: {},
    url : request.url
  };

  let originIP = request.headers.get("CF-Connecting-IP");
  if (originIP === null) {
    retJson.origin = "unknown";
  } else {
    retJson.origin = originIP;
  }

  for (const [key, value] of url.searchParams.entries()) {
    retJson.args[key] = value;
  }

  for (const [key, value] of request.headers.entries()) {
    retJson.headers[key] = value;
  }

  if (method === HttpMethod.POST) {
    if (contentType === ContentType.JSON) {
      const json = await request.json();
      retJson.json = json;
      retJson.data = JSON.stringify(json);
    } else if (contentType === ContentType.FORM) {
      const formData = await request.formData();
      retJson.form = {};
      for (const [key, value] of formData.entries()) {
        retJson.form[key] = value;
      }
    } else if (contentType === ContentType.TEXT) {
      const text = await request.text();
      retJson.data = text;
    }
  }

  let resp = new Response(JSON.stringify(retJson), {
    headers: {
      'content-type': 'application/json',
    },
  });
  return resp;
}

export { Parse };